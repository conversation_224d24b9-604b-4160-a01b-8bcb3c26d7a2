"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/settings/page",{

/***/ "(app-pages-browser)/./src/components/ImageUploader/ImageUploader.tsx":
/*!********************************************************!*\
  !*** ./src/components/ImageUploader/ImageUploader.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Upload,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.508.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Upload,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.508.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ImageUploader = ()=>{\n    _s();\n    const inputRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n    const [previewUrl, setPreviewUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleFileChange = (e)=>{\n        console.log(\"File change event: \", e);\n    };\n    const handleRemove = ()=>{\n        setPreviewUrl(null);\n    };\n    console.log(previewUrl);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [\n            previewUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-32 h-32 rounded-md overflow-hidden border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        src: previewUrl,\n                        alt: \"Uploaded Image\",\n                        fill: true,\n                        className: \"object-cover\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\neurablitz\\\\flurrysports\\\\packages\\\\client\\\\src\\\\components\\\\ImageUploader\\\\ImageUploader.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: \"absolute top-1 right-1 bg-white/80 text-red-500\",\n                        onClick: handleRemove,\n                        type: \"button\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"J:\\\\neurablitz\\\\flurrysports\\\\packages\\\\client\\\\src\\\\components\\\\ImageUploader\\\\ImageUploader.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"J:\\\\neurablitz\\\\flurrysports\\\\packages\\\\client\\\\src\\\\components\\\\ImageUploader\\\\ImageUploader.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"J:\\\\neurablitz\\\\flurrysports\\\\packages\\\\client\\\\src\\\\components\\\\ImageUploader\\\\ImageUploader.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                htmlFor: \"image\",\n                className: \"w-32 h-32 flex items-center justify-center border border-dashed rounded-md text-gray-500 text-sm cursor-pointer hover:bg-gray-100 transition\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"J:\\\\neurablitz\\\\flurrysports\\\\packages\\\\client\\\\src\\\\components\\\\ImageUploader\\\\ImageUploader.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"J:\\\\neurablitz\\\\flurrysports\\\\packages\\\\client\\\\src\\\\components\\\\ImageUploader\\\\ImageUploader.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                ref: inputRef,\n                type: \"file\",\n                id: \"image\",\n                accept: \"image/*\",\n                className: \"hidden\",\n                onChange: handleFileChange\n            }, void 0, false, {\n                fileName: \"J:\\\\neurablitz\\\\flurrysports\\\\packages\\\\client\\\\src\\\\components\\\\ImageUploader\\\\ImageUploader.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"J:\\\\neurablitz\\\\flurrysports\\\\packages\\\\client\\\\src\\\\components\\\\ImageUploader\\\\ImageUploader.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ImageUploader, \"+iS/BQs29+EhstgxfsypGqjds6M=\");\n_c = ImageUploader;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ImageUploader);\nvar _c;\n$RefreshReg$(_c, \"ImageUploader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ImageUploader/ImageUploader.tsx\n"));

/***/ })

});