"use client";

import React, { useState } from "react";
import Image from "next/image";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Upload, X } from "lucide-react";
import { Button } from "../ui/button";
import { ControllerRenderProps } from "react-hook-form";

interface ImageUploaderProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  field: ControllerRenderProps<any, string>;
}

const ImageUploader = ({ field }: ImageUploaderProps) => {
  const [previewUrl, setPreviewUrl] = useState<string | null>(field.value);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newFile = e.target.files?.[0];

    if (newFile) {
      const url = URL.createObjectURL(newFile);
      setPreviewUrl(url);
      field.onChange(url);
    }
  };

  const handleRemove = () => {
    setPreviewUrl(null);
    field.onChange("");
  };

  console.log(previewUrl);

  return (
    <div className="flex items-center gap-4">
      {previewUrl ? (
        <div className="relative w-32 h-32 rounded-md overflow-hidden border">
          <Image
            src={previewUrl}
            alt="Uploaded Image"
            fill
            className="object-cover"
          />
          <Button
            size="icon"
            variant="ghost"
            className="absolute top-1 right-1 bg-white/80 text-red-500 cursor-pointer"
            onClick={handleRemove}
            type="button"
          >
            <X size={16} />
          </Button>
        </div>
      ) : (
        <Label
          htmlFor="image"
          className="w-32 h-32 flex items-center justify-center border border-dashed rounded-md text-gray-500 text-sm cursor-pointer hover:bg-gray-100 transition"
        >
          <Upload className="w-6 h-6" />
        </Label>
      )}
      <Input
        type="file"
        id="image"
        accept="image/*"
        className="hidden"
        {...field}
      />
    </div>
  );
};

export default ImageUploader;
