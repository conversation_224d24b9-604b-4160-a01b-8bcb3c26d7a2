"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/settings/page",{

/***/ "(app-pages-browser)/./src/components/ImageUploader/ImageUploader.tsx":
/*!********************************************************!*\
  !*** ./src/components/ImageUploader/ImageUploader.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/api/image.js\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Upload,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.508.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Upload,X!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.508.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ImageUploader = (param)=>{\n    let { field } = param;\n    _s();\n    const inputRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n    const [previewUrl, setPreviewUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleFileChange = (e)=>{\n        console.log(\"File change event: \", e);\n    };\n    const handleRemove = ()=>{\n        setPreviewUrl(null);\n    };\n    console.log(previewUrl);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [\n            previewUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-32 h-32 rounded-md overflow-hidden border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: previewUrl,\n                        alt: \"Uploaded Image\",\n                        fill: true,\n                        className: \"object-cover\"\n                    }, void 0, false, {\n                        fileName: \"J:\\\\neurablitz\\\\flurrysports\\\\packages\\\\client\\\\src\\\\components\\\\ImageUploader\\\\ImageUploader.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        size: \"icon\",\n                        variant: \"ghost\",\n                        className: \"absolute top-1 right-1 bg-white/80 text-red-500\",\n                        onClick: handleRemove,\n                        type: \"button\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"J:\\\\neurablitz\\\\flurrysports\\\\packages\\\\client\\\\src\\\\components\\\\ImageUploader\\\\ImageUploader.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"J:\\\\neurablitz\\\\flurrysports\\\\packages\\\\client\\\\src\\\\components\\\\ImageUploader\\\\ImageUploader.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"J:\\\\neurablitz\\\\flurrysports\\\\packages\\\\client\\\\src\\\\components\\\\ImageUploader\\\\ImageUploader.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                htmlFor: \"image\",\n                className: \"w-32 h-32 flex items-center justify-center border border-dashed rounded-md text-gray-500 text-sm cursor-pointer hover:bg-gray-100 transition\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"J:\\\\neurablitz\\\\flurrysports\\\\packages\\\\client\\\\src\\\\components\\\\ImageUploader\\\\ImageUploader.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"J:\\\\neurablitz\\\\flurrysports\\\\packages\\\\client\\\\src\\\\components\\\\ImageUploader\\\\ImageUploader.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                ref: inputRef,\n                type: \"file\",\n                id: \"image\",\n                accept: \"image/*\",\n                className: \"hidden\",\n                ...field,\n                onChange: handleFileChange\n            }, void 0, false, {\n                fileName: \"J:\\\\neurablitz\\\\flurrysports\\\\packages\\\\client\\\\src\\\\components\\\\ImageUploader\\\\ImageUploader.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"J:\\\\neurablitz\\\\flurrysports\\\\packages\\\\client\\\\src\\\\components\\\\ImageUploader\\\\ImageUploader.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ImageUploader, \"+iS/BQs29+EhstgxfsypGqjds6M=\");\n_c = ImageUploader;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ImageUploader);\nvar _c;\n$RefreshReg$(_c, \"ImageUploader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ImageUploader/ImageUploader.tsx\n"));

/***/ })

});